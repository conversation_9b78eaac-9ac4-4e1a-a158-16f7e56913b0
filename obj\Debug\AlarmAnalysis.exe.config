﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System">
      <section name="DevExpress.LookAndFeel.Design.AppSettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
  </configSections>
  <applicationSettings>
    <DevExpress.LookAndFeel.Design.AppSettings>
      <setting name="DPIAwarenessMode" serializeAs="String">
        <value>System</value>
      </setting>
      <setting name="RegisterBonusSkins" serializeAs="String">
        <value>True</value>
      </setting>
    </DevExpress.LookAndFeel.Design.AppSettings>
  </applicationSettings>
  <connectionStrings>
    <add name="AlarmDatabase" 
         connectionString="Data Source=.;Initial Catalog=DaPeng_IOServer;Integrated Security=True" 
         providerName="System.Data.SqlClient" />
  </connectionStrings>
  <appSettings>
    <!-- Phase 2 配置参数 -->
    <add key="TopNCount" value="10" />

    <!-- Phase 3 配置参数 - 抖动检测 -->
    <add key="FlutterTimeWindowSeconds" value="60" />
    <add key="FlutterThreshold" value="3" />

    <!-- Phase 4 配置参数 - 报警风暴检测 -->
    <add key="FloodTimeWindowMinutes" value="10" />
    <add key="FloodThreshold" value="10" />

    <!-- Phase 4 配置参数 - 序列模式挖掘 -->
    <add key="SequenceTimeWindowSeconds" value="30" />
    <add key="SequenceMinSupport" value="50" />
    <add key="SequenceMinConfidence" value="80" />
    <add key="SequenceMaxPatterns" value="100" />

    <!-- Phase 3 配置参数 - 生命周期分析 -->
    <add key="LifecycleTimeoutHours" value="24" />
    <add key="TransientAlarmMaxDurationMinutes" value="5" />

    <!-- 日志配置 -->
    <add key="LogLevel" value="INFO" />
    <add key="EnablePerformanceLogging" value="true" />
  </appSettings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>

  <!-- log4net配置 -->
  <log4net>
    <!-- 控制台输出 -->
    <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date{HH:mm:ss} [%level] %logger - %message%newline" />
      </layout>
    </appender>

    <!-- 文件输出 -->
    <appender name="FileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="Logs/AlarmAnalysis.log" />
      <appendToFile value="true" />
      <rollingStyle value="Date" />
      <datePattern value="yyyyMMdd" />
      <maxSizeRollBackups value="10" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date{yyyy-MM-dd HH:mm:ss} [%level] %logger{1} - %message%newline" />
      </layout>
    </appender>

    <!-- 错误日志文件 -->
    <appender name="ErrorFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="Logs/AlarmAnalysis_Error.log" />
      <appendToFile value="true" />
      <rollingStyle value="Date" />
      <datePattern value="yyyyMMdd" />
      <maxSizeRollBackups value="10" />
      <threshold value="ERROR" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date{yyyy-MM-dd HH:mm:ss} [%level] %logger - %message%newline%exception" />
      </layout>
    </appender>

    <!-- 根日志记录器 -->
    <root>
      <level value="INFO" />
      <appender-ref ref="ConsoleAppender" />
      <appender-ref ref="FileAppender" />
      <appender-ref ref="ErrorFileAppender" />
    </root>

    <!-- 特定类的日志级别 -->
    <logger name="AlarmAnalysis.Analysis.AdvancedAlarmAnalyzer">
      <level value="DEBUG" />
    </logger>
  </log4net>
</configuration>