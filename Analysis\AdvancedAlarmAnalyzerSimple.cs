// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Phase3-SimpleAnalyzer"
//   Timestamp: "2025-01-24T16:30:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则), KISS"
//   Quality_Check: "简化版高级报警分析器，不依赖log4net和配置文件。"
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Collections.Generic;
using System.Linq;
using AlarmAnalysis.Common;
using AlarmAnalysis.Models;

namespace AlarmAnalysis.Analysis
{
    /// <summary>
    /// 简化版高级报警分析器 - Phase 3功能实现
    /// 提供报警生命周期分析、响应KPI计算和抖动检测
    /// </summary>
    public class AdvancedAlarmAnalyzerSimple : IDisposable
    {
        private readonly int _flutterTimeWindowSeconds;
        private readonly int _flutterThreshold;
        private bool _disposed = false;
        
        #region 构造函数
        
        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="flutterTimeWindowSeconds">抖动检测时间窗口（秒）</param>
        /// <param name="flutterThreshold">抖动检测阈值</param>
        public AdvancedAlarmAnalyzerSimple(int flutterTimeWindowSeconds = 60, int flutterThreshold = 3)
        {
            _flutterTimeWindowSeconds = flutterTimeWindowSeconds > 0 ? flutterTimeWindowSeconds : 60;
            _flutterThreshold = flutterThreshold > 0 ? flutterThreshold : 3;
        }
        
        #endregion
        
        #region 报警生命周期重构
        
        /// <summary>
        /// 重构报警生命周期，追踪每个报警实例的完整状态变迁
        /// </summary>
        /// <param name="alarmEvents">按时间排序的报警事件列表</param>
        /// <returns>报警生命周期字典</returns>
        public Dictionary<string, AlarmLifecycleSimple> ReconstructAlarmLifecycles(List<AlarmEvent> alarmEvents)
        {
            if (alarmEvents == null || alarmEvents.Count == 0)
            {
                return new Dictionary<string, AlarmLifecycleSimple>();
            }

            try
            {
                var lifecycles = new Dictionary<string, AlarmLifecycleSimple>();

                foreach (var alarmEvent in alarmEvents.OrderBy(e => e.EventDateTime))
                {
                    // 生成唯一标识符：SourceName + EventMessage + 初始触发时间戳
                    string lifecycleKey = GenerateLifecycleKey(alarmEvent);

                    if (!lifecycles.ContainsKey(lifecycleKey))
                    {
                        // 创建新的生命周期实例
                        lifecycles[lifecycleKey] = new AlarmLifecycleSimple
                        {
                            LifecycleId = lifecycleKey,
                            SourceName = alarmEvent.SourceName,
                            Station = alarmEvent.Station,
                            Device = alarmEvent.Device,
                            EventMessage = alarmEvent.EventMessage,
                            InitialTriggerTime = alarmEvent.EventDateTime,
                            StateTransitions = new List<AlarmStateTransition>()
                        };
                    }
                    
                    // 添加状态转换记录
                    var lifecycle = lifecycles[lifecycleKey];
                    lifecycle.StateTransitions.Add(new AlarmStateTransition
                    {
                        EventDateTime = alarmEvent.EventDateTime,
                        EventState = alarmEvent.EventState,
                        EventId = alarmEvent.EventId,
                        Severity = alarmEvent.Severity,
                        UserName = alarmEvent.UserName,
                        EventSequence = alarmEvent.EventSequence
                    });
                    
                    // 更新生命周期状态
                    UpdateLifecycleStatus(lifecycle, alarmEvent);
                }
                
                return lifecycles;
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"报警生命周期重构失败: {ex.Message}", ex, "LifecycleReconstruction", alarmEvents.Count);
            }
        }
        
        /// <summary>
        /// 生成生命周期唯一标识符
        /// </summary>
        /// <param name="alarmEvent">报警事件</param>
        /// <returns>唯一标识符</returns>
        private string GenerateLifecycleKey(AlarmEvent alarmEvent)
        {
            // 使用SourceName + EventMessage + 时间戳（精确到分钟）作为唯一标识
            var timeKey = alarmEvent.EventDateTime.ToString("yyyyMMddHHmm");
            return $"{alarmEvent.SourceName}|{alarmEvent.EventMessage}|{timeKey}";
        }
        
        /// <summary>
        /// 更新生命周期状态
        /// </summary>
        /// <param name="lifecycle">生命周期实例</param>
        /// <param name="alarmEvent">当前报警事件</param>
        private void UpdateLifecycleStatus(AlarmLifecycleSimple lifecycle, AlarmEvent alarmEvent)
        {
            try
            {
                // 更新最后更新时间
                lifecycle.LastUpdateTime = alarmEvent.EventDateTime;
                lifecycle.CurrentState = alarmEvent.EventState;
                
                // 根据状态更新关键时间点
                if (alarmEvent.IsActive && !lifecycle.FirstActiveTime.HasValue)
                {
                    lifecycle.FirstActiveTime = alarmEvent.EventDateTime;
                }
                
                if (alarmEvent.EventState.Contains("Acknowledged") && !lifecycle.AcknowledgedTime.HasValue)
                {
                    lifecycle.AcknowledgedTime = alarmEvent.EventDateTime;
                    lifecycle.AcknowledgedBy = alarmEvent.UserName;
                }
                
                if (alarmEvent.IsInactive && !lifecycle.ResolvedTime.HasValue)
                {
                    lifecycle.ResolvedTime = alarmEvent.EventDateTime;
                }
                
                // 计算持续时间
                if (lifecycle.FirstActiveTime.HasValue)
                {
                    lifecycle.TotalDuration = lifecycle.LastUpdateTime - lifecycle.FirstActiveTime.Value;
                }
                
                // 更新状态标志
                lifecycle.IsCurrentlyActive = alarmEvent.IsActive;
                lifecycle.IsAcknowledged = alarmEvent.EventState.Contains("Acknowledged");
                lifecycle.IsResolved = alarmEvent.IsInactive;
            }
            catch (Exception)
            {
                // 忽略更新错误，继续处理
            }
        }
        
        #endregion
        
        #region 响应KPI计算
        
        /// <summary>
        /// 计算响应KPI指标（TTA和TTR）
        /// </summary>
        /// <param name="lifecycles">报警生命周期字典</param>
        /// <returns>响应KPI结果</returns>
        public ResponseKPIResult CalculateResponseKPIs(Dictionary<string, AlarmLifecycleSimple> lifecycles)
        {
            if (lifecycles == null || lifecycles.Count == 0)
            {
                return new ResponseKPIResult();
            }
            
            try
            {
                var result = new ResponseKPIResult();
                var ttaValues = new List<double>();
                var ttrValues = new List<double>();
                
                foreach (var lifecycle in lifecycles.Values)
                {
                    // 收集TTA数据
                    if (lifecycle.TimeToAcknowledge.HasValue)
                    {
                        ttaValues.Add(lifecycle.TimeToAcknowledge.Value.TotalMinutes);
                    }
                    
                    // 收集TTR数据
                    if (lifecycle.TimeToResolve.HasValue)
                    {
                        ttrValues.Add(lifecycle.TimeToResolve.Value.TotalMinutes);
                    }
                }
                
                // 计算TTA统计
                result.TTAStatistics = CalculateTimeStatistics(ttaValues, "TTA");
                
                // 计算TTR统计
                result.TTRStatistics = CalculateTimeStatistics(ttrValues, "TTR");
                
                // 计算总体统计
                result.TotalLifecycles = lifecycles.Count;
                result.AcknowledgedCount = lifecycles.Values.Count(l => l.IsAcknowledged);
                result.ResolvedCount = lifecycles.Values.Count(l => l.IsResolved);
                result.ActiveCount = lifecycles.Values.Count(l => l.IsCurrentlyActive);
                
                result.AcknowledgmentRate = result.TotalLifecycles > 0 ? 
                    (double)result.AcknowledgedCount / result.TotalLifecycles * 100 : 0;
                result.ResolutionRate = result.TotalLifecycles > 0 ? 
                    (double)result.ResolvedCount / result.TotalLifecycles * 100 : 0;
                
                return result;
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"响应KPI计算失败: {ex.Message}", ex, "ResponseKPICalculation", lifecycles.Count);
            }
        }
        
        /// <summary>
        /// 计算时间统计指标
        /// </summary>
        /// <param name="values">时间值列表（分钟）</param>
        /// <param name="metricName">指标名称</param>
        /// <returns>时间统计结果</returns>
        private TimeStatistics CalculateTimeStatistics(List<double> values, string metricName)
        {
            var stats = new TimeStatistics { MetricName = metricName };
            
            if (values == null || values.Count == 0)
            {
                return stats;
            }
            
            var sortedValues = values.OrderBy(v => v).ToList();
            
            stats.SampleCount = values.Count;
            stats.MinValue = TimeSpan.FromMinutes(sortedValues.First());
            stats.MaxValue = TimeSpan.FromMinutes(sortedValues.Last());
            stats.AverageValue = TimeSpan.FromMinutes(values.Average());
            
            // 计算中位数
            if (sortedValues.Count % 2 == 0)
            {
                var mid1 = sortedValues[sortedValues.Count / 2 - 1];
                var mid2 = sortedValues[sortedValues.Count / 2];
                stats.MedianValue = TimeSpan.FromMinutes((mid1 + mid2) / 2);
            }
            else
            {
                stats.MedianValue = TimeSpan.FromMinutes(sortedValues[sortedValues.Count / 2]);
            }
            
            // 计算标准差
            var variance = values.Select(v => Math.Pow(v - values.Average(), 2)).Average();
            stats.StandardDeviation = TimeSpan.FromMinutes(Math.Sqrt(variance));
            
            // 计算百分位数
            stats.Percentile95 = TimeSpan.FromMinutes(GetPercentile(sortedValues, 0.95));
            stats.Percentile99 = TimeSpan.FromMinutes(GetPercentile(sortedValues, 0.99));
            
            return stats;
        }
        
        /// <summary>
        /// 计算百分位数
        /// </summary>
        /// <param name="sortedValues">已排序的数值列表</param>
        /// <param name="percentile">百分位数（0-1）</param>
        /// <returns>百分位数值</returns>
        private double GetPercentile(List<double> sortedValues, double percentile)
        {
            if (sortedValues.Count == 0) return 0;
            if (sortedValues.Count == 1) return sortedValues[0];
            
            double index = percentile * (sortedValues.Count - 1);
            int lowerIndex = (int)Math.Floor(index);
            int upperIndex = (int)Math.Ceiling(index);
            
            if (lowerIndex == upperIndex)
            {
                return sortedValues[lowerIndex];
            }
            
            double weight = index - lowerIndex;
            return sortedValues[lowerIndex] * (1 - weight) + sortedValues[upperIndex] * weight;
        }
        
        #endregion
        
        #region 抖动与瞬时报警检测
        
        /// <summary>
        /// 检测抖动报警
        /// </summary>
        /// <param name="alarmEvents">按时间排序的报警事件列表</param>
        /// <returns>抖动报警检测结果</returns>
        public List<FlutterAlarmResult> DetectFlutterAlarms(List<AlarmEvent> alarmEvents)
        {
            if (alarmEvents == null || alarmEvents.Count == 0)
            {
                return new List<FlutterAlarmResult>();
            }
            
            try
            {
                var flutterResults = new List<FlutterAlarmResult>();
                var sortedEvents = alarmEvents.OrderBy(e => e.EventDateTime).ToList();
                
                // 按SourceName + EventMessage分组
                var groupedEvents = sortedEvents
                    .GroupBy(e => $"{e.SourceName}|{e.EventMessage}")
                    .Where(g => g.Count() >= _flutterThreshold)
                    .ToList();
                
                foreach (var group in groupedEvents)
                {
                    var events = group.OrderBy(e => e.EventDateTime).ToList();
                    var flutterInstances = DetectFlutterInGroup(events);
                    flutterResults.AddRange(flutterInstances);
                }
                
                return flutterResults;
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"抖动报警检测失败: {ex.Message}", ex, "FlutterDetection", alarmEvents.Count);
            }
        }
        
        /// <summary>
        /// 在单个分组中检测抖动
        /// </summary>
        /// <param name="events">同一类型的报警事件列表</param>
        /// <returns>抖动实例列表</returns>
        private List<FlutterAlarmResult> DetectFlutterInGroup(List<AlarmEvent> events)
        {
            var flutterResults = new List<FlutterAlarmResult>();
            var windowSize = TimeSpan.FromSeconds(_flutterTimeWindowSeconds);
            
            // 使用滑动窗口算法
            for (int i = 0; i < events.Count; i++)
            {
                var windowStart = events[i].EventDateTime;
                var windowEnd = windowStart.Add(windowSize);
                
                // 收集窗口内的激活事件
                var activeEventsInWindow = events
                    .Where(e => e.EventDateTime >= windowStart && 
                               e.EventDateTime <= windowEnd && 
                               e.IsActive)
                    .ToList();
                
                if (activeEventsInWindow.Count >= _flutterThreshold)
                {
                    // 检查是否已经记录过这个时间段的抖动
                    bool alreadyRecorded = flutterResults.Any(f => 
                        Math.Abs((f.WindowStartTime - windowStart).TotalSeconds) < _flutterTimeWindowSeconds / 2);
                    
                    if (!alreadyRecorded)
                    {
                        var flutterResult = new FlutterAlarmResult
                        {
                            SourceName = events[i].SourceName,
                            Station = events[i].Station,
                            Device = events[i].Device,
                            EventMessage = events[i].EventMessage,
                            WindowStartTime = windowStart,
                            WindowEndTime = windowEnd,
                            WindowSize = windowSize,
                            ActivationCount = activeEventsInWindow.Count,
                            FlutterEvents = activeEventsInWindow.Select(e => new FlutterEventInfo
                            {
                                EventDateTime = e.EventDateTime,
                                EventId = e.EventId,
                                EventState = e.EventState,
                                Severity = e.Severity
                            }).ToList()
                        };
                        
                        flutterResults.Add(flutterResult);
                    }
                }
            }
            
            return flutterResults;
        }
        
        /// <summary>
        /// 检测瞬时报警
        /// </summary>
        /// <param name="lifecycles">报警生命周期字典</param>
        /// <returns>瞬时报警列表</returns>
        public List<TransientAlarmResult> DetectTransientAlarms(Dictionary<string, AlarmLifecycleSimple> lifecycles)
        {
            if (lifecycles == null || lifecycles.Count == 0)
            {
                return new List<TransientAlarmResult>();
            }
            
            try
            {
                var transientResults = new List<TransientAlarmResult>();
                
                foreach (var lifecycle in lifecycles.Values)
                {
                    if (lifecycle.IsTransientAlarm)
                    {
                        var transientResult = new TransientAlarmResult
                        {
                            LifecycleId = lifecycle.LifecycleId,
                            SourceName = lifecycle.SourceName,
                            Station = lifecycle.Station,
                            Device = lifecycle.Device,
                            EventMessage = lifecycle.EventMessage,
                            ActivationTime = lifecycle.FirstActiveTime ?? lifecycle.InitialTriggerTime,
                            DeactivationTime = lifecycle.ResolvedTime ?? lifecycle.LastUpdateTime,
                            Duration = lifecycle.TimeToResolve ?? lifecycle.TotalDuration,
                            StateTransitions = lifecycle.StateTransitions.Select(t => new TransientStateInfo
                            {
                                EventDateTime = t.EventDateTime,
                                EventState = t.EventState,
                                EventId = t.EventId
                            }).ToList()
                        };
                        
                        transientResults.Add(transientResult);
                    }
                }
                
                return transientResults;
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"瞬时报警检测失败: {ex.Message}", ex, "TransientDetection", lifecycles.Count);
            }
        }
        
        #endregion
        
        #region IDisposable实现
        
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                _disposed = true;
            }
        }
        
        #endregion
    }

    #region 数据模型定义

    /// <summary>
    /// 报警生命周期数据模型（简化版）
    /// </summary>
    public class AlarmLifecycleSimple
    {
        public string LifecycleId { get; set; }
        public string SourceName { get; set; }
        public string Station { get; set; }
        public string Device { get; set; }
        public string EventMessage { get; set; }
        public DateTime InitialTriggerTime { get; set; }
        public DateTime? FirstActiveTime { get; set; }
        public DateTime? AcknowledgedTime { get; set; }
        public string AcknowledgedBy { get; set; }
        public DateTime? ResolvedTime { get; set; }
        public DateTime LastUpdateTime { get; set; }
        public string CurrentState { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public bool IsCurrentlyActive { get; set; }
        public bool IsAcknowledged { get; set; }
        public bool IsResolved { get; set; }
        public List<AlarmStateTransition> StateTransitions { get; set; }

        public TimeSpan? TimeToAcknowledge
        {
            get
            {
                if (FirstActiveTime.HasValue && AcknowledgedTime.HasValue)
                {
                    return AcknowledgedTime.Value - FirstActiveTime.Value;
                }
                return null;
            }
        }

        public TimeSpan? TimeToResolve
        {
            get
            {
                if (FirstActiveTime.HasValue && ResolvedTime.HasValue)
                {
                    return ResolvedTime.Value - FirstActiveTime.Value;
                }
                return null;
            }
        }

        public bool IsTransientAlarm
        {
            get
            {
                if (StateTransitions == null || StateTransitions.Count < 2)
                    return false;

                var orderedTransitions = StateTransitions.OrderBy(t => t.EventDateTime).ToList();

                for (int i = 0; i < orderedTransitions.Count - 1; i++)
                {
                    var current = orderedTransitions[i];
                    var next = orderedTransitions[i + 1];

                    if (current.EventState == "Active | Unacknowledged" &&
                        next.EventState == "Inactive | Unacknowledged")
                    {
                        return true;
                    }
                }

                return false;
            }
        }
    }

    public class AlarmStateTransition
    {
        public DateTime EventDateTime { get; set; }
        public string EventState { get; set; }
        public string EventId { get; set; }
        public decimal Severity { get; set; }
        public string UserName { get; set; }
        public decimal EventSequence { get; set; }
    }

    public class ResponseKPIResult
    {
        public TimeStatistics TTAStatistics { get; set; } = new TimeStatistics();
        public TimeStatistics TTRStatistics { get; set; } = new TimeStatistics();
        public int TotalLifecycles { get; set; }
        public int AcknowledgedCount { get; set; }
        public int ResolvedCount { get; set; }
        public int ActiveCount { get; set; }
        public double AcknowledgmentRate { get; set; }
        public double ResolutionRate { get; set; }

        public string GetSummary()
        {
            return $"KPI Summary - Total: {TotalLifecycles}, Ack Rate: {AcknowledgmentRate:F1}%, " +
                   $"Resolution Rate: {ResolutionRate:F1}%, Avg TTA: {TTAStatistics.AverageValue.TotalMinutes:F1}min, " +
                   $"Avg TTR: {TTRStatistics.AverageValue.TotalMinutes:F1}min";
        }
    }

    public class TimeStatistics
    {
        public string MetricName { get; set; }
        public int SampleCount { get; set; }
        public TimeSpan MinValue { get; set; }
        public TimeSpan MaxValue { get; set; }
        public TimeSpan AverageValue { get; set; }
        public TimeSpan MedianValue { get; set; }
        public TimeSpan StandardDeviation { get; set; }
        public TimeSpan Percentile95 { get; set; }
        public TimeSpan Percentile99 { get; set; }
    }

    public class FlutterAlarmResult
    {
        public string SourceName { get; set; }
        public string Station { get; set; }
        public string Device { get; set; }
        public string EventMessage { get; set; }
        public DateTime WindowStartTime { get; set; }
        public DateTime WindowEndTime { get; set; }
        public TimeSpan WindowSize { get; set; }
        public int ActivationCount { get; set; }
        public List<FlutterEventInfo> FlutterEvents { get; set; } = new List<FlutterEventInfo>();

        public string GetSummary()
        {
            return $"Flutter - {Station}.{Device}: {EventMessage}, " +
                   $"Window: {WindowStartTime:HH:mm:ss}-{WindowEndTime:HH:mm:ss}, Count: {ActivationCount}";
        }
    }

    public class FlutterEventInfo
    {
        public DateTime EventDateTime { get; set; }
        public string EventId { get; set; }
        public string EventState { get; set; }
        public decimal Severity { get; set; }
    }

    public class TransientAlarmResult
    {
        public string LifecycleId { get; set; }
        public string SourceName { get; set; }
        public string Station { get; set; }
        public string Device { get; set; }
        public string EventMessage { get; set; }
        public DateTime ActivationTime { get; set; }
        public DateTime DeactivationTime { get; set; }
        public TimeSpan Duration { get; set; }
        public List<TransientStateInfo> StateTransitions { get; set; } = new List<TransientStateInfo>();

        public string GetSummary()
        {
            return $"Transient - {Station}.{Device}: {EventMessage}, " +
                   $"Duration: {Duration.TotalMinutes:F2}min, Time: {ActivationTime:HH:mm:ss}";
        }
    }

    public class TransientStateInfo
    {
        public DateTime EventDateTime { get; set; }
        public string EventState { get; set; }
        public string EventId { get; set; }
    }

    #endregion
}
// {{END_MODIFICATIONS}}
