// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Phase2-ServiceLayer"
//   Timestamp: "2024-12-19T11:30:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "Aether-Engineering-SOLID-S, Aether-Engineering-Facade-Pattern"
//   Quality_Check: "Service layer providing unified interface for alarm analysis operations."
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AlarmAnalysis.Analysis;
using AlarmAnalysis.Common;
using AlarmAnalysis.DataAccess;
using AlarmAnalysis.Models;

namespace AlarmAnalysis.Services
{
    /// <summary>
    /// 报警分析服务，提供统一的分析操作接口
    /// </summary>
    public class AlarmAnalysisService : IDisposable
    {
        private readonly AlarmDataReader _dataReader;
        private readonly BasicAlarmAnalyzer _analyzer;
        private readonly AdvancedAlarmAnalyzer _advancedAnalyzer;
        private readonly AlarmFloodAnalyzer _floodAnalyzer;
        private readonly SequencePatternMiner _patternMiner;
        private bool _disposed = false;
        
        #region 构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public AlarmAnalysisService()
        {
            _dataReader = new AlarmDataReader();
            _analyzer = new BasicAlarmAnalyzer();
            _advancedAnalyzer = new AdvancedAlarmAnalyzer();
            _floodAnalyzer = new AlarmFloodAnalyzer();
            _patternMiner = new SequencePatternMiner();
        }
        
        /// <summary>
        /// 带连接字符串的构造函数
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        public AlarmAnalysisService(string connectionString)
        {
            _dataReader = new AlarmDataReader(connectionString);
            _analyzer = new BasicAlarmAnalyzer();
            _advancedAnalyzer = new AdvancedAlarmAnalyzer();
            _floodAnalyzer = new AlarmFloodAnalyzer();
            _patternMiner = new SequencePatternMiner();
        }
        
        #endregion
        
        #region 数据加载
        
        /// <summary>
        /// 加载指定时间范围的报警数据
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="tableName">表名</param>
        /// <returns>报警事件列表</returns>
        public List<AlarmEvent> LoadAlarmData(DateTime startTime, DateTime endTime, string tableName = "AlarmHistory")
        {
            try
            {
                ValidateTimeRange(startTime, endTime);
                return _dataReader.ReadAlarmEvents(startTime, endTime, tableName);
            }
            catch (Exception ex)
            {
                throw new DataAccessException($"加载报警数据失败: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 异步加载指定时间范围的报警数据
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="tableName">表名</param>
        /// <returns>报警事件列表</returns>
        public async Task<List<AlarmEvent>> LoadAlarmDataAsync(DateTime startTime, DateTime endTime, string tableName = "AlarmHistory")
        {
            try
            {
                ValidateTimeRange(startTime, endTime);
                return await _dataReader.ReadAlarmEventsAsync(startTime, endTime, tableName);
            }
            catch (Exception ex)
            {
                throw new DataAccessException($"异步加载报警数据失败: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 流式加载报警数据
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="onDataReceived">数据接收回调</param>
        /// <param name="batchSize">批处理大小</param>
        /// <param name="tableName">表名</param>
        public void LoadAlarmDataStream(DateTime startTime, DateTime endTime, 
            Action<List<AlarmEvent>> onDataReceived, int batchSize = 1000, string tableName = "AlarmHistory")
        {
            try
            {
                ValidateTimeRange(startTime, endTime);
                _dataReader.ReadAlarmEventsStream(startTime, endTime, onDataReceived, batchSize, tableName);
            }
            catch (Exception ex)
            {
                throw new DataAccessException($"流式加载报警数据失败: {ex.Message}", ex);
            }
        }
        
        #endregion
        
        #region Phase 2 分析功能
        
        /// <summary>
        /// 执行完整的基础分析
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <param name="topN">Top N数量</param>
        /// <returns>完整分析结果</returns>
        public CompleteAnalysisResult PerformCompleteAnalysis(List<AlarmEvent> alarmEvents, int? topN = null)
        {
            try
            {
                if (alarmEvents == null)
                    throw new ArgumentNullException(nameof(alarmEvents));
                
                var result = new CompleteAnalysisResult
                {
                    AnalysisTime = DateTime.Now,
                    DataTimeRange = GetDataTimeRange(alarmEvents),
                    TotalEvents = alarmEvents.Count
                };
                
                // Top N 分析
                result.TopAlarmMessages = _analyzer.GetTopFrequentAlarmMessages(alarmEvents, topN);
                result.TopAlarmingDevices = _analyzer.GetTopAlarmingDevices(alarmEvents, topN);
                result.TopAlarmingStations = _analyzer.GetTopAlarmingStations(alarmEvents, topN);
                
                // 报警率分析
                result.AlarmRates = _analyzer.CalculateAlarmRates(alarmEvents);
                
                // 持续和陈旧报警分析
                result.LongStandingAlarms = _analyzer.IdentifyLongStandingAlarms(alarmEvents);
                result.StaleAlarms = _analyzer.IdentifyStaleAlarms(alarmEvents);
                
                return result;
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"执行完整分析失败: {ex.Message}", ex, "CompleteAnalysis", alarmEvents?.Count ?? 0);
            }
        }
        
        /// <summary>
        /// 获取Top N最频繁报警消息
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <param name="topN">Top N数量</param>
        /// <returns>分析结果</returns>
        public List<AlarmFrequencyResult> GetTopFrequentAlarms(List<AlarmEvent> alarmEvents, int? topN = null)
        {
            try
            {
                return _analyzer.GetTopFrequentAlarmMessages(alarmEvents, topN);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"Top N报警分析失败: {ex.Message}", ex, "TopFrequentAlarms", alarmEvents?.Count ?? 0);
            }
        }
        
        /// <summary>
        /// 获取报警最多的设备
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <param name="topN">Top N数量</param>
        /// <returns>分析结果</returns>
        public List<AlarmFrequencyResult> GetTopAlarmingDevices(List<AlarmEvent> alarmEvents, int? topN = null)
        {
            try
            {
                return _analyzer.GetTopAlarmingDevices(alarmEvents, topN);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"设备报警分析失败: {ex.Message}", ex, "TopAlarmingDevices", alarmEvents?.Count ?? 0);
            }
        }
        
        /// <summary>
        /// 获取报警最多的站点
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <param name="topN">Top N数量</param>
        /// <returns>分析结果</returns>
        public List<AlarmFrequencyResult> GetTopAlarmingStations(List<AlarmEvent> alarmEvents, int? topN = null)
        {
            try
            {
                return _analyzer.GetTopAlarmingStations(alarmEvents, topN);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"站点报警分析失败: {ex.Message}", ex, "TopAlarmingStations", alarmEvents?.Count ?? 0);
            }
        }
        
        /// <summary>
        /// 计算报警率
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>报警率结果</returns>
        public AlarmRateResult CalculateAlarmRates(List<AlarmEvent> alarmEvents)
        {
            try
            {
                return _analyzer.CalculateAlarmRates(alarmEvents);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"报警率计算失败: {ex.Message}", ex, "AlarmRates", alarmEvents?.Count ?? 0);
            }
        }
        
        /// <summary>
        /// 识别长期持续报警
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>长期持续报警列表</returns>
        public List<LongStandingAlarmResult> IdentifyLongStandingAlarms(List<AlarmEvent> alarmEvents)
        {
            try
            {
                return _analyzer.IdentifyLongStandingAlarms(alarmEvents);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"长期持续报警识别失败: {ex.Message}", ex, "LongStandingAlarms", alarmEvents?.Count ?? 0);
            }
        }
        
        /// <summary>
        /// 识别陈旧报警
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>陈旧报警列表</returns>
        public List<StaleAlarmResult> IdentifyStaleAlarms(List<AlarmEvent> alarmEvents)
        {
            try
            {
                return _analyzer.IdentifyStaleAlarms(alarmEvents);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"陈旧报警识别失败: {ex.Message}", ex, "StaleAlarms", alarmEvents?.Count ?? 0);
            }
        }
        
        #endregion
        
        #region 工具方法
        
        /// <summary>
        /// 测试数据库连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        public bool TestDatabaseConnection()
        {
            return _dataReader.TestConnection();
        }
        
        /// <summary>
        /// 异步测试数据库连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        public async Task<bool> TestDatabaseConnectionAsync()
        {
            return await _dataReader.TestConnectionAsync();
        }
        
        /// <summary>
        /// 获取记录总数
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>记录总数</returns>
        public long GetRecordCount(string tableName = "AlarmHistory", DateTime? startTime = null, DateTime? endTime = null)
        {
            return _dataReader.GetRecordCount(tableName, startTime, endTime);
        }
        
        #endregion
        
        #region 私有辅助方法
        
        /// <summary>
        /// 验证时间范围
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        private static void ValidateTimeRange(DateTime startTime, DateTime endTime)
        {
            if (startTime >= endTime)
            {
                throw new ValidationException("开始时间必须小于结束时间", "TimeRange", $"StartTime: {startTime}, EndTime: {endTime}");
            }
            
            if (endTime > DateTime.Now)
            {
                throw new ValidationException("结束时间不能超过当前时间", "TimeRange", $"EndTime: {endTime}, Now: {DateTime.Now}");
            }
        }
        
        /// <summary>
        /// 获取数据时间范围
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>时间范围字符串</returns>
        private static string GetDataTimeRange(List<AlarmEvent> alarmEvents)
        {
            if (alarmEvents == null || !alarmEvents.Any())
                return "无数据";
            
            var minTime = alarmEvents.Min(e => e.EventDateTime);
            var maxTime = alarmEvents.Max(e => e.EventDateTime);
            
            return $"{minTime:yyyy-MM-dd HH:mm:ss} - {maxTime:yyyy-MM-dd HH:mm:ss}";
        }
        
        #endregion

        #region Phase 3 高级分析方法

        /// <summary>
        /// 执行报警生命周期重构分析
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>生命周期字典</returns>
        public Dictionary<string, AlarmLifecycle> AnalyzeAlarmLifecycles(List<AlarmEvent> alarmEvents)
        {
            try
            {
                return _advancedAnalyzer.ReconstructAlarmLifecycles(alarmEvents);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"报警生命周期分析失败: {ex.Message}", ex, "LifecycleAnalysis", alarmEvents?.Count ?? 0);
            }
        }

        /// <summary>
        /// 计算响应KPI指标
        /// </summary>
        /// <param name="lifecycles">生命周期字典</param>
        /// <returns>KPI结果</returns>
        public ResponseKPIResult CalculateResponseKPIs(Dictionary<string, AlarmLifecycle> lifecycles)
        {
            try
            {
                return _advancedAnalyzer.CalculateResponseKPIs(lifecycles);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"响应KPI计算失败: {ex.Message}", ex, "ResponseKPICalculation", lifecycles?.Count ?? 0);
            }
        }

        /// <summary>
        /// 检测抖动报警
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>抖动报警列表</returns>
        public List<FlutterAlarmResult> DetectFlutterAlarms(List<AlarmEvent> alarmEvents)
        {
            try
            {
                return _advancedAnalyzer.DetectFlutterAlarms(alarmEvents);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"抖动报警检测失败: {ex.Message}", ex, "FlutterDetection", alarmEvents?.Count ?? 0);
            }
        }

        /// <summary>
        /// 检测瞬时报警
        /// </summary>
        /// <param name="lifecycles">生命周期字典</param>
        /// <returns>瞬时报警列表</returns>
        public List<TransientAlarmResult> DetectTransientAlarms(Dictionary<string, AlarmLifecycle> lifecycles)
        {
            try
            {
                return _advancedAnalyzer.DetectTransientAlarms(lifecycles);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"瞬时报警检测失败: {ex.Message}", ex, "TransientDetection", lifecycles?.Count ?? 0);
            }
        }

        /// <summary>
        /// 执行完整的Phase 3分析
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>Phase 3分析结果</returns>
        public Phase3AnalysisResult PerformPhase3Analysis(List<AlarmEvent> alarmEvents)
        {
            try
            {
                var result = new Phase3AnalysisResult
                {
                    AnalysisTime = DateTime.Now,
                    TotalEvents = alarmEvents?.Count ?? 0
                };

                if (alarmEvents == null || alarmEvents.Count == 0)
                {
                    return result;
                }

                // 生命周期重构
                result.AlarmLifecycles = AnalyzeAlarmLifecycles(alarmEvents);

                // KPI计算
                result.ResponseKPIs = CalculateResponseKPIs(result.AlarmLifecycles);

                // 抖动检测
                result.FlutterAlarms = DetectFlutterAlarms(alarmEvents);

                // 瞬时报警检测
                result.TransientAlarms = DetectTransientAlarms(result.AlarmLifecycles);

                return result;
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"执行Phase 3分析失败: {ex.Message}", ex, "Phase3Analysis", alarmEvents?.Count ?? 0);
            }
        }

        #endregion

        #region Phase 4 高级关联与序列分析方法

        /// <summary>
        /// 检测报警风暴事件
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>报警风暴结果列表</returns>
        public List<AlarmFloodResult> DetectAlarmFloods(List<AlarmEvent> alarmEvents)
        {
            try
            {
                return _floodAnalyzer.DetectAlarmFloods(alarmEvents);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"报警风暴检测失败: {ex.Message}", ex, "FloodDetection", alarmEvents?.Count ?? 0);
            }
        }

        /// <summary>
        /// 挖掘序列模式，发现强关联的报警序列
        /// </summary>
        /// <param name="alarmEvents">报警事件列表（应该是经过抖动过滤的数据）</param>
        /// <returns>序列模式结果</returns>
        public SequencePatternResult MineSequencePatterns(List<AlarmEvent> alarmEvents)
        {
            try
            {
                return _patternMiner.MineSequencePatterns(alarmEvents);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"序列模式挖掘失败: {ex.Message}", ex, "SequencePatternMining", alarmEvents?.Count ?? 0);
            }
        }

        /// <summary>
        /// 执行完整的Phase 4分析
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>Phase 4分析结果</returns>
        public Phase4AnalysisResult PerformPhase4Analysis(List<AlarmEvent> alarmEvents)
        {
            try
            {
                var result = new Phase4AnalysisResult
                {
                    AnalysisTime = DateTime.Now,
                    TotalEvents = alarmEvents?.Count ?? 0
                };

                if (alarmEvents == null || alarmEvents.Count == 0)
                {
                    return result;
                }

                // 首先过滤抖动报警，避免无意义的自身关联
                var filteredEvents = FilterFlutterAlarms(alarmEvents);

                // 报警风暴检测
                result.AlarmFloods = DetectAlarmFloods(alarmEvents);

                // 序列模式挖掘（使用过滤后的数据）
                result.SequencePatterns = MineSequencePatterns(filteredEvents);

                return result;
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"执行Phase 4分析失败: {ex.Message}", ex, "Phase4Analysis", alarmEvents?.Count ?? 0);
            }
        }

        /// <summary>
        /// 过滤抖动报警，返回清理后的数据用于序列分析
        /// </summary>
        /// <param name="alarmEvents">原始报警事件列表</param>
        /// <returns>过滤后的报警事件列表</returns>
        private List<AlarmEvent> FilterFlutterAlarms(List<AlarmEvent> alarmEvents)
        {
            try
            {
                // 检测抖动报警
                var flutterAlarms = _advancedAnalyzer.DetectFlutterAlarms(alarmEvents);

                // 创建抖动报警的标识集合
                var flutterKeys = new HashSet<string>();
                foreach (var flutter in flutterAlarms)
                {
                    var key = $"{flutter.SourceName}|{flutter.EventMessage}";
                    flutterKeys.Add(key);
                }

                // 过滤掉抖动报警
                var filteredEvents = alarmEvents.Where(e =>
                {
                    var key = $"{e.SourceName}|{e.EventMessage}";
                    return !flutterKeys.Contains(key);
                }).ToList();

                return filteredEvents;
            }
            catch (Exception ex)
            {
                // 如果过滤失败，返回原始数据
                return alarmEvents;
            }
        }

        #endregion

        #region IDisposable实现
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        
        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _dataReader?.Dispose();
                _advancedAnalyzer?.Dispose();
                _floodAnalyzer?.Dispose();
                _patternMiner?.Dispose();
                _disposed = true;
            }
        }
        
        /// <summary>
        /// 析构函数
        /// </summary>
        ~AlarmAnalysisService()
        {
            Dispose(false);
        }
        
        #endregion
    }
    
    /// <summary>
    /// 完整分析结果
    /// </summary>
    public class CompleteAnalysisResult
    {
        public DateTime AnalysisTime { get; set; }
        public string DataTimeRange { get; set; }
        public int TotalEvents { get; set; }
        
        // Phase 2 分析结果
        public List<AlarmFrequencyResult> TopAlarmMessages { get; set; }
        public List<AlarmFrequencyResult> TopAlarmingDevices { get; set; }
        public List<AlarmFrequencyResult> TopAlarmingStations { get; set; }
        public AlarmRateResult AlarmRates { get; set; }
        public List<LongStandingAlarmResult> LongStandingAlarms { get; set; }
        public List<StaleAlarmResult> StaleAlarms { get; set; }
        
        /// <summary>
        /// 获取分析摘要
        /// </summary>
        /// <returns>摘要字符串</returns>
        public string GetSummary()
        {
            var summary = new System.Text.StringBuilder();
            summary.AppendLine($"分析时间: {AnalysisTime:yyyy-MM-dd HH:mm:ss}");
            summary.AppendLine($"数据时间范围: {DataTimeRange}");
            summary.AppendLine($"总报警数: {TotalEvents:N0}");
            summary.AppendLine($"平均报警率: {AlarmRates?.AlarmsPerHour:F2} 次/小时");
            summary.AppendLine($"峰值报警率: {AlarmRates?.PeakAlarmRate?.MaxAlarmsInWindow:N0} 次/{AlarmRates?.PeakAlarmRate?.WindowSize.TotalMinutes:F0}分钟");
            summary.AppendLine($"长期持续报警: {LongStandingAlarms?.Count:N0} 项");
            summary.AppendLine($"陈旧报警: {StaleAlarms?.Count:N0} 项");
            
            return summary.ToString();
        }
    }

    /// <summary>
    /// Phase 3分析结果
    /// </summary>
    public class Phase3AnalysisResult
    {
        /// <summary>
        /// 分析时间
        /// </summary>
        public DateTime AnalysisTime { get; set; }

        /// <summary>
        /// 总事件数
        /// </summary>
        public int TotalEvents { get; set; }

        /// <summary>
        /// 报警生命周期字典
        /// </summary>
        public Dictionary<string, AlarmLifecycle> AlarmLifecycles { get; set; } = new Dictionary<string, AlarmLifecycle>();

        /// <summary>
        /// 响应KPI结果
        /// </summary>
        public ResponseKPIResult ResponseKPIs { get; set; } = new ResponseKPIResult();

        /// <summary>
        /// 抖动报警列表
        /// </summary>
        public List<FlutterAlarmResult> FlutterAlarms { get; set; } = new List<FlutterAlarmResult>();

        /// <summary>
        /// 瞬时报警列表
        /// </summary>
        public List<TransientAlarmResult> TransientAlarms { get; set; } = new List<TransientAlarmResult>();

        /// <summary>
        /// 获取分析摘要
        /// </summary>
        /// <returns>摘要字符串</returns>
        public string GetSummary()
        {
            var summary = new System.Text.StringBuilder();
            summary.AppendLine($"=== Phase 3 高级分析结果 ===");
            summary.AppendLine($"分析时间: {AnalysisTime:yyyy-MM-dd HH:mm:ss}");
            summary.AppendLine($"总事件数: {TotalEvents:N0}");
            summary.AppendLine($"生命周期实例: {AlarmLifecycles.Count:N0}");
            summary.AppendLine();

            summary.AppendLine($"=== 响应KPI ===");
            summary.AppendLine(ResponseKPIs.GetSummary());
            summary.AppendLine();

            summary.AppendLine($"=== 抖动报警 ===");
            summary.AppendLine($"检测到抖动报警: {FlutterAlarms.Count} 个");
            foreach (var flutter in FlutterAlarms.Take(5))
            {
                summary.AppendLine($"  - {flutter.GetSummary()}");
            }
            if (FlutterAlarms.Count > 5)
            {
                summary.AppendLine($"  ... 还有 {FlutterAlarms.Count - 5} 个抖动报警");
            }
            summary.AppendLine();

            summary.AppendLine($"=== 瞬时报警 ===");
            summary.AppendLine($"检测到瞬时报警: {TransientAlarms.Count} 个");
            foreach (var transient in TransientAlarms.Take(5))
            {
                summary.AppendLine($"  - {transient.GetSummary()}");
            }
            if (TransientAlarms.Count > 5)
            {
                summary.AppendLine($"  ... 还有 {TransientAlarms.Count - 5} 个瞬时报警");
            }

            return summary.ToString();
        }
    }
}
// {{END_MODIFICATIONS}}
