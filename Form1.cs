using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using AlarmAnalysis.Analysis;
using AlarmAnalysis.DataAccess;
using AlarmAnalysis.Models;
using AlarmAnalysis.Services;

namespace AlarmAnalysis
{
    public partial class Form1 : DevExpress.XtraEditors.XtraForm
    {
        public Form1()
        {
            InitializeComponent();
        }

        /// <summary>
        /// Phase 3测试按钮点击事件 - 使用真实数据库数据
        /// </summary>
        private void btnTestPhase_Click(object sender, EventArgs e)
        {
            try
            {
                // 清空输出控件
                memoEdit1.Text = "";

                // 显示开始信息
                AppendOutput("=== Phase 3 高级报警分析测试（真实数据库数据）===\n");
                Application.DoEvents();

                // 从数据库加载真实数据
                var realData = LoadRealDatabaseData();
                AppendOutput($"从数据库加载了 {realData.Count} 条真实记录\n");
                Application.DoEvents();

                // 测试1: 生命周期重构
                TestLifecycleReconstruction(realData);

                // 测试2: KPI计算
                TestKPICalculation(realData);

                // 测试3: 抖动检测
                TestFlutterDetection(realData);

                // 测试4: 瞬时报警检测
                TestTransientDetection(realData);

                // 测试5: 详细统计分析
                TestDetailedStatistics(realData);

                // 测试6: 性能测试
                TestPerformance();

                AppendOutput("\n=== 所有测试成功通过！ ===");
                AppendOutput("Phase 3 功能实现正常工作。");
            }
            catch (Exception ex)
            {
                AppendOutput($"❌ 测试失败: {ex.Message}");
                AppendOutput($"详细信息: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 向输出控件添加文本
        /// </summary>
        private void AppendOutput(string text)
        {
            memoEdit1.Text += text + "\r\n";
            memoEdit1.SelectionStart = memoEdit1.Text.Length;
            memoEdit1.ScrollToCaret();
            Application.DoEvents();
        }

        /// <summary>
        /// 生成测试数据
        /// </summary>
        private List<AlarmEvent> GenerateTestData()
        {
            var testData = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddHours(-12);

            // 生成正常报警序列
            for (int i = 0; i < 15; i++)
            {
                var alarmTime = baseTime.AddMinutes(i * 20);

                // 激活事件
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"TestStation/Device_{i % 3}/Source",
                    EventMessage = $"Test Alarm {i % 2}",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = 100,
                    EventSequence = i * 3
                });

                // 确认事件 (70%的概率)
                if (random.Next(10) < 7)
                {
                    testData.Add(new AlarmEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        SourceName = $"TestStation/Device_{i % 3}/Source",
                        EventMessage = $"Test Alarm {i % 2}",
                        EventState = "Active | Acknowledged",
                        EventDateTime = alarmTime.AddMinutes(random.Next(2, 8)),
                        Severity = 100,
                        UserName = "TestUser",
                        EventSequence = i * 3 + 1
                    });

                    // 解决事件 (80%的概率)
                    if (random.Next(10) < 8)
                    {
                        testData.Add(new AlarmEvent
                        {
                            EventId = Guid.NewGuid().ToString(),
                            SourceName = $"TestStation/Device_{i % 3}/Source",
                            EventMessage = $"Test Alarm {i % 2}",
                            EventState = "Inactive | Acknowledged",
                            EventDateTime = alarmTime.AddMinutes(random.Next(10, 25)),
                            Severity = 100,
                            UserName = "TestUser",
                            EventSequence = i * 3 + 2
                        });
                    }
                }
            }

            // 生成抖动报警 (在60秒内激活4次)
            var flutterTime = baseTime.AddHours(6);
            for (int i = 0; i < 4; i++)
            {
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = "TestStation/FlutterDevice/Source",
                    EventMessage = "Flutter Alarm",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = flutterTime.AddSeconds(i * 15), // 每15秒激活一次
                    Severity = 100,
                    EventSequence = 1000 + i
                });
            }

            // 生成瞬时报警 (直接从Active变为Inactive)
            var transientTime = baseTime.AddHours(8);
            testData.Add(new AlarmEvent
            {
                EventId = Guid.NewGuid().ToString(),
                SourceName = "TestStation/TransientDevice/Source",
                EventMessage = "Transient Alarm",
                EventState = "Active | Unacknowledged",
                EventDateTime = transientTime,
                Severity = 100,
                EventSequence = 2000
            });

            testData.Add(new AlarmEvent
            {
                EventId = Guid.NewGuid().ToString(),
                SourceName = "TestStation/TransientDevice/Source",
                EventMessage = "Transient Alarm",
                EventState = "Inactive | Unacknowledged",
                EventDateTime = transientTime.AddMinutes(1.5), // 1.5分钟后直接变为Inactive
                Severity = 100,
                EventSequence = 2001
            });

            return testData.OrderBy(e => e.EventDateTime).ToList();
        }

        /// <summary>
        /// 测试生命周期重构功能
        /// </summary>
        private void TestLifecycleReconstruction(List<AlarmEvent> testData)
        {
            AppendOutput("🔄 测试 1: 生命周期重构");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3)) // 使用参数构造函数避免配置依赖
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);

                    AppendOutput($"  ✓ 生成了 {lifecycles.Count} 个生命周期");

                    var sampleLifecycle = lifecycles.Values.FirstOrDefault();
                    if (sampleLifecycle != null)
                    {
                        AppendOutput($"  示例: {sampleLifecycle.Station}.{sampleLifecycle.Device}");
                        AppendOutput($"    确认时长: {sampleLifecycle.TimeToAcknowledge?.TotalMinutes:F1} 分钟");
                        AppendOutput($"    解决时长: {sampleLifecycle.TimeToResolve?.TotalMinutes:F1} 分钟");
                        AppendOutput($"    瞬时报警: {sampleLifecycle.IsTransientAlarm}");
                    }
                }
                AppendOutput("  ✓ 通过");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ 生命周期重构测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试KPI计算功能
        /// </summary>
        private void TestKPICalculation(List<AlarmEvent> testData)
        {
            AppendOutput("\n📊 测试 2: KPI 计算");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);
                    var kpis = analyzer.CalculateResponseKPIs(lifecycles);

                    AppendOutput($"  ✓ KPI 计算完成");
                    AppendOutput($"    总生命周期数: {kpis.TotalLifecycles}");
                    AppendOutput($"    确认率: {kpis.AcknowledgmentRate:F1}%");
                    AppendOutput($"    解决率: {kpis.ResolutionRate:F1}%");
                    AppendOutput($"    平均确认时长: {kpis.TTAStatistics.AverageValue.TotalMinutes:F1} 分钟");
                    AppendOutput($"    平均解决时长: {kpis.TTRStatistics.AverageValue.TotalMinutes:F1} 分钟");
                    AppendOutput($"    确认时长中位数: {kpis.TTAStatistics.MedianValue.TotalMinutes:F1} 分钟");
                    AppendOutput($"    解决时长中位数: {kpis.TTRStatistics.MedianValue.TotalMinutes:F1} 分钟");
                }
                AppendOutput("  ✓ 通过");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ KPI 计算测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试抖动检测功能
        /// </summary>
        private void TestFlutterDetection(List<AlarmEvent> testData)
        {
            AppendOutput("\n⚡ 测试 3: 抖动检测");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var flutters = analyzer.DetectFlutterAlarms(testData);

                    AppendOutput($"  ✓ 检测到 {flutters.Count} 个抖动报警");

                    foreach (var flutter in flutters.Take(3))
                    {
                        AppendOutput($"    {flutter.Station}.{flutter.Device}: 在 {flutter.WindowSize.TotalSeconds} 秒内激活 {flutter.ActivationCount} 次");
                    }
                }
                AppendOutput("  ✓ 通过");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ 抖动检测测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试瞬时报警检测功能
        /// </summary>
        private void TestTransientDetection(List<AlarmEvent> testData)
        {
            AppendOutput("\n⚡ 测试 4: 瞬时报警检测");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);
                    var transients = analyzer.DetectTransientAlarms(lifecycles);

                    AppendOutput($"  ✓ 检测到 {transients.Count} 个瞬时报警");

                    foreach (var transient in transients.Take(3))
                    {
                        AppendOutput($"    {transient.Station}.{transient.Device}: 持续时间 {transient.Duration.TotalMinutes:F1} 分钟");
                    }
                }
                AppendOutput("  ✓ 通过");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ 瞬时报警检测测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 性能测试
        /// </summary>
        private void TestPerformance()
        {
            AppendOutput("\n🚀 测试 5: 性能测试");

            try
            {
                var largeDataset = GenerateLargeDataset(2000);
                AppendOutput($"  生成测试数据: {largeDataset.Count:N0} 条记录");

                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(largeDataset);
                    var kpis = analyzer.CalculateResponseKPIs(lifecycles);
                    var flutters = analyzer.DetectFlutterAlarms(largeDataset);
                    var transients = analyzer.DetectTransientAlarms(lifecycles);

                    stopwatch.Stop();

                    AppendOutput($"  ✓ 大数据集分析完成");
                    AppendOutput($"    处理时间: {stopwatch.ElapsedMilliseconds:N0} 毫秒");
                    AppendOutput($"    处理速度: {largeDataset.Count / stopwatch.Elapsed.TotalSeconds:F0} 条记录/秒");
                    AppendOutput($"    生成了 {lifecycles.Count:N0} 个生命周期");
                    AppendOutput($"    发现 {flutters.Count:N0} 个抖动报警");
                    AppendOutput($"    发现 {transients.Count:N0} 个瞬时报警");
                }
                AppendOutput("  ✓ 通过");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ 性能测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 生成大数据集用于性能测试
        /// </summary>
        private List<AlarmEvent> GenerateLargeDataset(int count)
        {
            var dataset = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddDays(-2);

            var stations = new[] { "Station1", "Station2", "Station3", "Station4" };
            var devices = new[] { "Device1", "Device2", "Device3", "Device4", "Device5" };
            var messages = new[] { "Temperature Alarm", "Pressure Alarm", "Flow Alarm", "Level Alarm" };

            for (int i = 0; i < count; i++)
            {
                var station = stations[random.Next(stations.Length)];
                var device = devices[random.Next(devices.Length)];
                var message = messages[random.Next(messages.Length)];

                var alarmTime = baseTime.AddMinutes(i * 0.5 + random.Next(0, 30));

                dataset.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"{station}/{device}/Source",
                    EventMessage = message,
                    EventState = random.Next(10) < 8 ? "Active | Unacknowledged" : "Inactive | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = random.Next(50, 101),
                    EventSequence = i,
                    UserName = random.Next(10) < 3 ? "TestUser" : null
                });
            }

            return dataset.OrderBy(e => e.EventDateTime).ToList();
        }

        /// <summary>
        /// Phase 4测试按钮点击事件 - 使用真实数据库数据
        /// </summary>
        private void btnTestPhase4_Click(object sender, EventArgs e)
        {
            try
            {
                // 清空输出控件
                memoEdit1.Text = "";

                // 显示开始信息
                AppendOutput("=== Phase 4 高级关联与序列分析测试（真实数据库数据）===\n");
                Application.DoEvents();

                // 从数据库加载真实数据
                var realData = LoadRealDatabaseData();
                AppendOutput($"从数据库加载了 {realData.Count} 条真实记录\n");
                Application.DoEvents();

                // 测试1: 报警风暴检测
                TestFloodDetection(realData);

                // 测试2: 序列模式挖掘
                TestSequencePatternMining(realData);

                // 测试3: 完整Phase 4分析
                TestCompletePhase4Analysis(realData);

                // 测试4: 性能测试
                TestPhase4Performance();

                AppendOutput("\n=== 所有测试成功通过！ ===");
                AppendOutput("Phase 4 功能实现正常工作。");
            }
            catch (Exception ex)
            {
                AppendOutput($"❌ 测试失败: {ex.Message}");
                AppendOutput($"详细信息: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 生成Phase 4专用测试数据
        /// </summary>
        private List<AlarmEvent> GeneratePhase4TestData()
        {
            var testData = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddHours(-6);

            // 生成正常报警序列
            for (int i = 0; i < 20; i++)
            {
                var alarmTime = baseTime.AddMinutes(i * 15);

                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"Station{i % 3}/Device{i % 4}/Source",
                    EventMessage = $"Normal Alarm {i % 3}",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = 100,
                    EventSequence = i
                });
            }

            // 生成报警风暴（10分钟内15个报警）
            var floodTime = baseTime.AddHours(2);
            for (int i = 0; i < 15; i++)
            {
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"FloodStation/Device{i % 3}/Source",
                    EventMessage = $"Flood Alarm {i % 2}",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = floodTime.AddMinutes(i * 0.5), // 每30秒一个
                    Severity = 100,
                    EventSequence = 1000 + i
                });
            }

            // 生成关联序列（A -> B 模式）
            var sequenceTime = baseTime.AddHours(4);
            for (int i = 0; i < 10; i++)
            {
                // 前件报警
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"SequenceStation/DeviceA/Source",
                    EventMessage = "Pressure High",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = sequenceTime.AddMinutes(i * 30),
                    Severity = 100,
                    EventSequence = 2000 + i * 2
                });

                // 后件报警（15秒后）
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"SequenceStation/DeviceB/Source",
                    EventMessage = "Temperature High",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = sequenceTime.AddMinutes(i * 30).AddSeconds(15),
                    Severity = 100,
                    EventSequence = 2000 + i * 2 + 1
                });
            }

            return testData.OrderBy(e => e.EventDateTime).ToList();
        }

        /// <summary>
        /// 测试报警风暴检测功能
        /// </summary>
        private void TestFloodDetection(List<AlarmEvent> testData)
        {
            AppendOutput("🌊 测试 1: 报警风暴检测");

            try
            {
                using (var analyzer = new AlarmFloodAnalyzer(10, 10)) // 10分钟窗口，10个报警阈值
                {
                    var floods = analyzer.DetectAlarmFloods(testData);

                    AppendOutput($"  ✓ 检测到 {floods.Count} 个报警风暴");

                    foreach (var flood in floods.Take(3))
                    {
                        AppendOutput($"    风暴时间: {flood.StartTime:HH:mm:ss}-{flood.EndTime:HH:mm:ss}");
                        AppendOutput($"    报警数量: {flood.TotalAlarmCount}, 峰值率: {flood.PeakAlarmRate:F1}/分钟");
                        AppendOutput($"    首出报警: {flood.FirstOutAlarm?.EventMessage}");
                        AppendOutput($"    涉及站点: {string.Join(", ", flood.AffectedStations)}");
                    }
                }
                AppendOutput("  ✓ 通过");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ 报警风暴检测测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试序列模式挖掘功能
        /// </summary>
        private void TestSequencePatternMining(List<AlarmEvent> testData)
        {
            AppendOutput("\n🔗 测试 2: 序列模式挖掘");

            try
            {
                using (var miner = new SequencePatternMiner(30, 5, 70.0, 50)) // 30秒窗口，支持度≥5，置信度≥70%
                {
                    var patterns = miner.MineSequencePatterns(testData);

                    AppendOutput($"  ✓ 序列模式挖掘完成");
                    AppendOutput($"    总事件数: {patterns.TotalEvents}");
                    AppendOutput($"    候选序列对: {patterns.TotalCandidatePairs}");
                    AppendOutput($"    强关联规则: {patterns.StrongRulesCount}");

                    foreach (var rule in patterns.AssociationRules.Take(3))
                    {
                        AppendOutput($"    规则: {rule.AntecedentMessage} → {rule.ConsequentMessage}");
                        AppendOutput($"      支持度: {rule.Support}, 置信度: {rule.Confidence:F1}%");
                        AppendOutput($"      平均间隔: {rule.AverageTimeDifference.TotalSeconds:F1} 秒");
                    }
                }
                AppendOutput("  ✓ 通过");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ 序列模式挖掘测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试完整Phase 4分析功能
        /// </summary>
        private void TestCompletePhase4Analysis(List<AlarmEvent> testData)
        {
            AppendOutput("\n🔬 测试 3: 完整 Phase 4 分析");

            try
            {
                using (var service = new AlarmAnalysisService())
                {
                    var result = service.PerformPhase4Analysis(testData);

                    AppendOutput($"  ✓ 完整分析完成");
                    AppendOutput($"    分析时间: {result.AnalysisTime:HH:mm:ss}");
                    AppendOutput($"    总事件数: {result.TotalEvents}");
                    AppendOutput($"    检测到风暴: {result.AlarmFloods.Count} 个");
                    AppendOutput($"    强关联规则: {result.SequencePatterns.StrongRulesCount} 个");
                    AppendOutput($"    摘要: {result.GetSummary()}");
                }
                AppendOutput("  ✓ 通过");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ 完整 Phase 4 分析测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Phase 4性能测试
        /// </summary>
        private void TestPhase4Performance()
        {
            AppendOutput("\n🚀 测试 4: Phase 4 性能测试");

            try
            {
                var largeDataset = GeneratePhase4LargeDataset(1000);
                AppendOutput($"  生成测试数据: {largeDataset.Count:N0} 条记录");

                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                using (var service = new AlarmAnalysisService())
                {
                    var result = service.PerformPhase4Analysis(largeDataset);

                    stopwatch.Stop();

                    AppendOutput($"  ✓ 大数据集分析完成");
                    AppendOutput($"    处理时间: {stopwatch.ElapsedMilliseconds:N0} 毫秒");
                    AppendOutput($"    处理速度: {largeDataset.Count / stopwatch.Elapsed.TotalSeconds:F0} 条记录/秒");
                    AppendOutput($"    检测到风暴: {result.AlarmFloods.Count:N0} 个");
                    AppendOutput($"    强关联规则: {result.SequencePatterns.StrongRulesCount:N0} 个");
                }
                AppendOutput("  ✓ 通过");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ Phase 4 性能测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 生成Phase 4大数据集用于性能测试
        /// </summary>
        private List<AlarmEvent> GeneratePhase4LargeDataset(int count)
        {
            var dataset = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddDays(-1);

            var stations = new[] { "Station1", "Station2", "Station3", "Station4", "Station5" };
            var devices = new[] { "Device1", "Device2", "Device3", "Device4", "Device5", "Device6" };
            var messages = new[] { "Temperature Alarm", "Pressure Alarm", "Flow Alarm", "Level Alarm", "Vibration Alarm" };

            for (int i = 0; i < count; i++)
            {
                var station = stations[random.Next(stations.Length)];
                var device = devices[random.Next(devices.Length)];
                var message = messages[random.Next(messages.Length)];

                var alarmTime = baseTime.AddMinutes(i * 0.2 + random.Next(0, 10));

                dataset.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"{station}/{device}/Source",
                    EventMessage = message,
                    EventState = random.Next(10) < 8 ? "Active | Unacknowledged" : "Inactive | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = random.Next(50, 101),
                    EventSequence = i,
                    UserName = random.Next(10) < 3 ? "TestUser" : null
                });

                // 偶尔生成风暴模式
                if (i % 100 == 0 && i > 0)
                {
                    var floodTime = alarmTime.AddMinutes(5);
                    for (int j = 0; j < 12; j++)
                    {
                        dataset.Add(new AlarmEvent
                        {
                            EventId = Guid.NewGuid().ToString(),
                            SourceName = $"{station}/FloodDevice/Source",
                            EventMessage = "Flood Pattern Alarm",
                            EventState = "Active | Unacknowledged",
                            EventDateTime = floodTime.AddSeconds(j * 30),
                            Severity = 100,
                            EventSequence = i * 1000 + j
                        });
                    }
                }

                // 偶尔生成关联序列
                if (i % 50 == 0 && i > 0)
                {
                    var seqTime = alarmTime.AddMinutes(10);
                    dataset.Add(new AlarmEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        SourceName = $"{station}/SeqDeviceA/Source",
                        EventMessage = "Sequence Start",
                        EventState = "Active | Unacknowledged",
                        EventDateTime = seqTime,
                        Severity = 100,
                        EventSequence = i * 2000
                    });

                    dataset.Add(new AlarmEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        SourceName = $"{station}/SeqDeviceB/Source",
                        EventMessage = "Sequence Follow",
                        EventState = "Active | Unacknowledged",
                        EventDateTime = seqTime.AddSeconds(20),
                        Severity = 100,
                        EventSequence = i * 2000 + 1
                    });
                }
            }

            return dataset.OrderBy(e => e.EventDateTime).ToList();
        }

        /// <summary>
        /// 从真实数据库加载UFUAAuditLogItem表数据
        /// </summary>
        /// <returns>报警事件列表</returns>
        private List<AlarmEvent> LoadRealDatabaseData()
        {
            try
            {
                AppendOutput("正在连接数据库...");
                Application.DoEvents();

                using (var dataReader = new AlarmDataReader())
                {
                    // 获取最近7天的数据
                    var endTime = DateTime.Now;
                    var startTime = endTime.AddDays(-120);

                    AppendOutput($"查询时间范围: {startTime:yyyy-MM-dd HH:mm:ss} 到 {endTime:yyyy-MM-dd HH:mm:ss}");
                    Application.DoEvents();

                    // 从UFUAAuditLogItem表读取数据
                    var alarmEvents = dataReader.ReadAlarmEvents(startTime, endTime, "UFUAAuditLogItem");

                    AppendOutput($"数据库连接成功，查询完成");
                    Application.DoEvents();

                    if (alarmEvents.Count == 0)
                    {
                        AppendOutput("⚠️ 警告：数据库中没有找到指定时间范围内的数据");
                        AppendOutput("将使用生成的测试数据进行演示...");
                        Application.DoEvents();

                        // 如果没有真实数据，返回生成的测试数据
                        return GenerateTestData();
                    }

                    AppendOutput($"✅ 成功加载 {alarmEvents.Count} 条真实数据");

                    // 显示数据统计信息
                    var stations = alarmEvents.Select(e => e.Station).Distinct().Count();
                    var devices = alarmEvents.Select(e => e.Device).Distinct().Count();
                    var alarmTypes = alarmEvents.Select(e => e.EventMessage).Distinct().Count();

                    AppendOutput($"数据统计: {stations} 个站点, {devices} 个设备, {alarmTypes} 种报警类型");
                    Application.DoEvents();

                    return alarmEvents;
                }
            }
            catch (Exception ex)
            {
                AppendOutput($"❌ 数据库连接失败: {ex.Message}");
                AppendOutput("将使用生成的测试数据进行演示...");
                Application.DoEvents();

                // 如果数据库连接失败，返回生成的测试数据作为备用
                return GenerateTestData();
            }
        }

        /// <summary>
        /// 测试详细统计分析功能
        /// </summary>
        private void TestDetailedStatistics(List<AlarmEvent> testData)
        {
            AppendOutput("\n📊 测试 5: 详细统计分析");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    // 重构生命周期
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);
                    var kpis = analyzer.CalculateResponseKPIs(lifecycles);

                    AppendOutput($"  ✓ 生命周期重构完成，共 {lifecycles.Count} 个生命周期");

                    // 1. 处理时间最长的报警 Top 10
                    AppendOutput("\n  📈 处理时间最长的报警 Top 10:");
                    var longestProcessingAlarms = lifecycles.Values
                        .Where(l => l.TimeToResolve.HasValue)
                        .OrderByDescending(l => l.TimeToResolve.Value.TotalMinutes)
                        .Take(10)
                        .ToList();

                    if (longestProcessingAlarms.Any())
                    {
                        for (int i = 0; i < longestProcessingAlarms.Count; i++)
                        {
                            var alarm = longestProcessingAlarms[i];
                            AppendOutput($"    {i + 1:D2}. {alarm.Station}.{alarm.Device} - {alarm.EventMessage}");
                            AppendOutput($"        处理时长: {alarm.TimeToResolve.Value.TotalHours:F1} 小时 ({alarm.TimeToResolve.Value.TotalMinutes:F0} 分钟)");
                            AppendOutput($"        触发时间: {alarm.FirstActiveTime:yyyy-MM-dd HH:mm:ss}");
                            AppendOutput($"        解决时间: {alarm.ResolvedTime:yyyy-MM-dd HH:mm:ss}");
                        }
                    }
                    else
                    {
                        AppendOutput("    暂无已解决的报警数据");
                    }

                    // 2. 处理时间最短的报警 Top 10
                    AppendOutput("\n  ⚡ 处理时间最短的报警 Top 10:");
                    var shortestProcessingAlarms = lifecycles.Values
                        .Where(l => l.TimeToResolve.HasValue && l.TimeToResolve.Value.TotalMinutes > 0)
                        .OrderBy(l => l.TimeToResolve.Value.TotalMinutes)
                        .Take(10)
                        .ToList();

                    if (shortestProcessingAlarms.Any())
                    {
                        for (int i = 0; i < shortestProcessingAlarms.Count; i++)
                        {
                            var alarm = shortestProcessingAlarms[i];
                            AppendOutput($"    {i + 1:D2}. {alarm.Station}.{alarm.Device} - {alarm.EventMessage}");
                            AppendOutput($"        处理时长: {alarm.TimeToResolve.Value.TotalMinutes:F1} 分钟");
                            AppendOutput($"        触发时间: {alarm.FirstActiveTime:yyyy-MM-dd HH:mm:ss}");
                            AppendOutput($"        解决时间: {alarm.ResolvedTime:yyyy-MM-dd HH:mm:ss}");
                        }
                    }
                    else
                    {
                        AppendOutput("    暂无快速处理的报警数据");
                    }

                    // 3. 触发次数最多的报警 Top 10
                    AppendOutput("\n  🔥 触发次数最多的报警 Top 10:");
                    var mostFrequentAlarms = testData
                        .GroupBy(e => new { e.Station, e.Device, e.EventMessage })
                        .Select(g => new
                        {
                            Station = g.Key.Station,
                            Device = g.Key.Device,
                            EventMessage = g.Key.EventMessage,
                            Count = g.Count(),
                            FirstOccurrence = g.Min(e => e.EventDateTime),
                            LastOccurrence = g.Max(e => e.EventDateTime),
                            TimeSpan = g.Max(e => e.EventDateTime) - g.Min(e => e.EventDateTime),
                            AverageSeverity = g.Average(e => (double)e.Severity)
                        })
                        .OrderByDescending(x => x.Count)
                        .Take(10)
                        .ToList();

                    for (int i = 0; i < mostFrequentAlarms.Count; i++)
                    {
                        var alarm = mostFrequentAlarms[i];
                        AppendOutput($"    {i + 1:D2}. {alarm.Station}.{alarm.Device} - {alarm.EventMessage}");
                        AppendOutput($"        触发次数: {alarm.Count} 次");
                        AppendOutput($"        时间跨度: {alarm.TimeSpan.TotalDays:F1} 天");
                        AppendOutput($"        首次触发: {alarm.FirstOccurrence:yyyy-MM-dd HH:mm:ss}");
                        AppendOutput($"        最后触发: {alarm.LastOccurrence:yyyy-MM-dd HH:mm:ss}");
                        AppendOutput($"        平均严重程度: {alarm.AverageSeverity:F1}");
                        if (alarm.Count > 1)
                        {
                            var avgInterval = alarm.TimeSpan.TotalMinutes / (alarm.Count - 1);
                            AppendOutput($"        平均间隔: {avgInterval:F1} 分钟");
                        }
                    }

                    // 4. 确认时间最长的报警 Top 10
                    AppendOutput("\n  ⏰ 确认时间最长的报警 Top 10:");
                    var longestAcknowledgeAlarms = lifecycles.Values
                        .Where(l => l.TimeToAcknowledge.HasValue)
                        .OrderByDescending(l => l.TimeToAcknowledge.Value.TotalMinutes)
                        .Take(10)
                        .ToList();

                    if (longestAcknowledgeAlarms.Any())
                    {
                        for (int i = 0; i < longestAcknowledgeAlarms.Count; i++)
                        {
                            var alarm = longestAcknowledgeAlarms[i];
                            AppendOutput($"    {i + 1:D2}. {alarm.Station}.{alarm.Device} - {alarm.EventMessage}");
                            AppendOutput($"        确认时长: {alarm.TimeToAcknowledge.Value.TotalHours:F1} 小时 ({alarm.TimeToAcknowledge.Value.TotalMinutes:F0} 分钟)");
                            AppendOutput($"        触发时间: {alarm.FirstActiveTime:yyyy-MM-dd HH:mm:ss}");
                            AppendOutput($"        确认时间: {alarm.AcknowledgedTime:yyyy-MM-dd HH:mm:ss}");
                            AppendOutput($"        确认人员: {alarm.AcknowledgedBy ?? "未知"}");
                        }
                    }
                    else
                    {
                        AppendOutput("    暂无已确认的报警数据");
                    }

                    // 5. 统计摘要
                    AppendOutput("\n  📋 统计摘要:");
                    AppendOutput($"    总报警事件数: {testData.Count:N0}");
                    AppendOutput($"    生命周期数: {lifecycles.Count:N0}");
                    AppendOutput($"    已确认报警: {kpis.AcknowledgedCount:N0} ({kpis.AcknowledgmentRate:F1}%)");
                    AppendOutput($"    已解决报警: {kpis.ResolvedCount:N0} ({kpis.ResolutionRate:F1}%)");
                    AppendOutput($"    当前活跃报警: {kpis.ActiveCount:N0}");

                    if (kpis.TTAStatistics.SampleCount > 0)
                    {
                        AppendOutput($"    平均确认时长: {kpis.TTAStatistics.AverageValue.TotalMinutes:F1} 分钟");
                        AppendOutput($"    确认时长范围: {kpis.TTAStatistics.MinValue.TotalMinutes:F1} - {kpis.TTAStatistics.MaxValue.TotalMinutes:F1} 分钟");
                    }

                    if (kpis.TTRStatistics.SampleCount > 0)
                    {
                        AppendOutput($"    平均解决时长: {kpis.TTRStatistics.AverageValue.TotalHours:F1} 小时");
                        AppendOutput($"    解决时长范围: {kpis.TTRStatistics.MinValue.TotalHours:F1} - {kpis.TTRStatistics.MaxValue.TotalHours:F1} 小时");
                    }
                }

                AppendOutput("  ✓ 通过");
            }
            catch (Exception ex)
            {
                AppendOutput($"  ❌ 详细统计分析测试失败: {ex.Message}");
                throw;
            }
        }
    }
}
