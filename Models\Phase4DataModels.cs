using System;
using System.Collections.Generic;
using System.Linq;

namespace AlarmAnalysis.Models
{
    #region Phase 4 分析结果

    /// <summary>
    /// Phase 4分析结果 - 高级关联与序列分析
    /// </summary>
    public class Phase4AnalysisResult
    {
        /// <summary>
        /// 分析时间
        /// </summary>
        public DateTime AnalysisTime { get; set; }

        /// <summary>
        /// 总事件数
        /// </summary>
        public int TotalEvents { get; set; }

        /// <summary>
        /// 报警风暴分析结果
        /// </summary>
        public List<AlarmFloodResult> AlarmFloods { get; set; } = new List<AlarmFloodResult>();

        /// <summary>
        /// 序列模式挖掘结果
        /// </summary>
        public SequencePatternResult SequencePatterns { get; set; } = new SequencePatternResult();

        /// <summary>
        /// 获取分析摘要
        /// </summary>
        /// <returns>摘要字符串</returns>
        public string GetSummary()
        {
            return $"Phase 4 高级关联分析摘要 - 总事件: {TotalEvents}, " +
                   $"检测到风暴: {AlarmFloods.Count}个, " +
                   $"强关联规则: {SequencePatterns.StrongRulesCount}个, " +
                   $"分析时间: {AnalysisTime:yyyy-MM-dd HH:mm:ss}";
        }
    }

    #endregion

    #region 报警风暴相关数据模型

    /// <summary>
    /// 报警风暴分析结果
    /// </summary>
    public class AlarmFloodResult
    {
        /// <summary>
        /// 风暴开始时间（实际）
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 风暴结束时间（实际）
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 风暴持续时长
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 检测窗口开始时间
        /// </summary>
        public DateTime WindowStartTime { get; set; }

        /// <summary>
        /// 检测窗口结束时间
        /// </summary>
        public DateTime WindowEndTime { get; set; }

        /// <summary>
        /// 风暴期间总报警数量
        /// </summary>
        public int TotalAlarmCount { get; set; }

        /// <summary>
        /// 峰值报警率（每分钟最大报警数）
        /// </summary>
        public double PeakAlarmRate { get; set; }

        /// <summary>
        /// 首出报警（First-Out Alarm）
        /// </summary>
        public FloodAlarmInfo FirstOutAlarm { get; set; }

        /// <summary>
        /// 涉及的站点列表
        /// </summary>
        public List<string> AffectedStations { get; set; } = new List<string>();

        /// <summary>
        /// 涉及的设备列表
        /// </summary>
        public List<string> AffectedDevices { get; set; } = new List<string>();

        /// <summary>
        /// 唯一报警类型数量
        /// </summary>
        public int UniqueAlarmTypes { get; set; }

        /// <summary>
        /// 风暴期间的所有报警事件
        /// </summary>
        public List<FloodAlarmInfo> FloodEvents { get; set; } = new List<FloodAlarmInfo>();

        /// <summary>
        /// 获取风暴摘要
        /// </summary>
        /// <returns>摘要字符串</returns>
        public string GetSummary()
        {
            return $"报警风暴 - 时间: {StartTime:HH:mm:ss}-{EndTime:HH:mm:ss}, " +
                   $"持续: {Duration.TotalMinutes:F1}分钟, 报警数: {TotalAlarmCount}, " +
                   $"峰值率: {PeakAlarmRate:F1}/分钟, 首出: {FirstOutAlarm?.EventMessage}";
        }
    }

    /// <summary>
    /// 风暴报警信息
    /// </summary>
    public class FloodAlarmInfo
    {
        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventDateTime { get; set; }

        /// <summary>
        /// 报警源名称
        /// </summary>
        public string SourceName { get; set; }

        /// <summary>
        /// 报警消息
        /// </summary>
        public string EventMessage { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        public string Station { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string Device { get; set; }

        /// <summary>
        /// 严重程度
        /// </summary>
        public int Severity { get; set; }
    }

    #endregion

    #region 序列模式相关数据模型

    /// <summary>
    /// 序列模式挖掘结果
    /// </summary>
    public class SequencePatternResult
    {
        /// <summary>
        /// 分析时间
        /// </summary>
        public DateTime AnalysisTime { get; set; }

        /// <summary>
        /// 总事件数
        /// </summary>
        public int TotalEvents { get; set; }

        /// <summary>
        /// 时间窗口大小
        /// </summary>
        public TimeSpan TimeWindow { get; set; }

        /// <summary>
        /// 最小支持度阈值
        /// </summary>
        public int MinSupport { get; set; }

        /// <summary>
        /// 最小置信度阈值（百分比）
        /// </summary>
        public double MinConfidence { get; set; }

        /// <summary>
        /// 总候选序列对数量
        /// </summary>
        public int TotalCandidatePairs { get; set; }

        /// <summary>
        /// 强关联规则数量
        /// </summary>
        public int StrongRulesCount { get; set; }

        /// <summary>
        /// 关联规则列表
        /// </summary>
        public List<AssociationRule> AssociationRules { get; set; } = new List<AssociationRule>();

        /// <summary>
        /// 获取序列模式摘要
        /// </summary>
        /// <returns>摘要字符串</returns>
        public string GetSummary()
        {
            return $"序列模式挖掘摘要 - 事件数: {TotalEvents}, 时间窗口: {TimeWindow.TotalSeconds}秒, " +
                   $"候选对: {TotalCandidatePairs}, 强规则: {StrongRulesCount}, " +
                   $"阈值: 支持度≥{MinSupport}, 置信度≥{MinConfidence}%";
        }
    }

    /// <summary>
    /// 关联规则
    /// </summary>
    public class AssociationRule
    {
        /// <summary>
        /// 前件报警消息
        /// </summary>
        public string AntecedentMessage { get; set; }

        /// <summary>
        /// 后件报警消息
        /// </summary>
        public string ConsequentMessage { get; set; }

        /// <summary>
        /// 支持度（发生次数）
        /// </summary>
        public int Support { get; set; }

        /// <summary>
        /// 置信度（百分比）
        /// </summary>
        public double Confidence { get; set; }

        /// <summary>
        /// 平均时间间隔
        /// </summary>
        public TimeSpan AverageTimeDifference { get; set; }

        /// <summary>
        /// 涉及的站点对
        /// </summary>
        public List<string> StationPairs { get; set; } = new List<string>();

        /// <summary>
        /// 涉及的设备对
        /// </summary>
        public List<string> DevicePairs { get; set; } = new List<string>();

        /// <summary>
        /// 具体发生的序列对实例
        /// </summary>
        public List<SequencePair> Occurrences { get; set; } = new List<SequencePair>();

        /// <summary>
        /// 获取关联规则摘要
        /// </summary>
        /// <returns>摘要字符串</returns>
        public string GetSummary()
        {
            return $"关联规则: {AntecedentMessage} → {ConsequentMessage}, " +
                   $"支持度: {Support}, 置信度: {Confidence:F1}%, " +
                   $"平均间隔: {AverageTimeDifference.TotalSeconds:F1}秒";
        }
    }

    /// <summary>
    /// 序列对
    /// </summary>
    public class SequencePair
    {
        /// <summary>
        /// 前件报警消息
        /// </summary>
        public string AntecedentMessage { get; set; }

        /// <summary>
        /// 后件报警消息
        /// </summary>
        public string ConsequentMessage { get; set; }

        /// <summary>
        /// 前件站点
        /// </summary>
        public string AntecedentStation { get; set; }

        /// <summary>
        /// 后件站点
        /// </summary>
        public string ConsequentStation { get; set; }

        /// <summary>
        /// 前件设备
        /// </summary>
        public string AntecedentDevice { get; set; }

        /// <summary>
        /// 后件设备
        /// </summary>
        public string ConsequentDevice { get; set; }

        /// <summary>
        /// 时间差
        /// </summary>
        public TimeSpan TimeDifference { get; set; }
    }

    #endregion
}
