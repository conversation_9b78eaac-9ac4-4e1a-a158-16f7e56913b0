// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Phase1-Configuration"
//   Timestamp: "2024-12-19T11:15:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "Aether-Engineering-SOLID-S, Aether-Engineering-DRY"
//   Quality_Check: "Centralized configuration management with validation and defaults."
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;

namespace AlarmAnalysis.Common
{
    /// <summary>
    /// 配置辅助类，提供类型安全的配置读取和默认值处理
    /// </summary>
    public static class ConfigurationHelper
    {
        #region 数据库配置
        
        /// <summary>
        /// 获取数据库连接字符串
        /// </summary>
        /// <param name="name">连接字符串名称，默认为"AlarmDatabase"</param>
        /// <returns>连接字符串</returns>
        /// <exception cref="ConfigurationErrorsException">连接字符串不存在时抛出异常</exception>
        public static string GetConnectionString(string name = "AlarmDatabase")
        {
            var connectionString = ConfigurationManager.ConnectionStrings[name]?.ConnectionString;
            if (string.IsNullOrWhiteSpace(connectionString))
            {
                throw new ConfigurationErrorsException($"未找到名为'{name}'的连接字符串配置");
            }
            return connectionString;
        }
        
        /// <summary>
        /// 尝试获取数据库连接字符串
        /// </summary>
        /// <param name="name">连接字符串名称</param>
        /// <param name="connectionString">输出的连接字符串</param>
        /// <returns>是否成功获取</returns>
        public static bool TryGetConnectionString(string name, out string connectionString)
        {
            connectionString = ConfigurationManager.ConnectionStrings[name]?.ConnectionString;
            return !string.IsNullOrWhiteSpace(connectionString);
        }
        
        #endregion
        
        #region 应用程序设置
        
        /// <summary>
        /// 获取整数配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public static int GetIntValue(string key, int defaultValue = 0)
        {
            var value = ConfigurationManager.AppSettings[key];
            return int.TryParse(value, out int result) ? result : defaultValue;
        }
        
        /// <summary>
        /// 获取双精度浮点配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public static double GetDoubleValue(string key, double defaultValue = 0.0)
        {
            var value = ConfigurationManager.AppSettings[key];
            return double.TryParse(value, out double result) ? result : defaultValue;
        }
        
        /// <summary>
        /// 获取布尔配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public static bool GetBoolValue(string key, bool defaultValue = false)
        {
            var value = ConfigurationManager.AppSettings[key];
            return bool.TryParse(value, out bool result) ? result : defaultValue;
        }
        
        /// <summary>
        /// 获取字符串配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public static string GetStringValue(string key, string defaultValue = "")
        {
            return ConfigurationManager.AppSettings[key] ?? defaultValue;
        }
        


        /// <summary>
        /// 获取TimeSpan配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public static TimeSpan GetTimeSpanValue(string key, TimeSpan defaultValue = default(TimeSpan))
        {
            var value = ConfigurationManager.AppSettings[key];
            return TimeSpan.TryParse(value, out TimeSpan result) ? result : defaultValue;
        }
        
        #endregion
        
        #region 分析参数配置
        
        /// <summary>
        /// 获取Top N统计的默认数量
        /// </summary>
        public static int TopNCount => GetIntValue("TopNCount", 10);
        
        /// <summary>
        /// 获取抖动检测的时间窗口（秒）
        /// </summary>
        public static int FlutterTimeWindowSeconds => GetIntValue("FlutterTimeWindowSeconds", 60);
        
        /// <summary>
        /// 获取抖动检测的阈值（次数）
        /// </summary>
        public static int FlutterThreshold => GetIntValue("FlutterThreshold", 3);
        
        /// <summary>
        /// 获取报警风暴检测的时间窗口（分钟）
        /// </summary>
        public static int FloodTimeWindowMinutes => GetIntValue("FloodTimeWindowMinutes", 10);
        
        /// <summary>
        /// 获取报警风暴检测的阈值（报警数量）
        /// </summary>
        public static int FloodThreshold => GetIntValue("FloodThreshold", 10);
        
        /// <summary>
        /// 获取数据库查询超时时间（秒）
        /// </summary>
        public static int DatabaseTimeoutSeconds => GetIntValue("DatabaseTimeoutSeconds", 30);
        
        /// <summary>
        /// 获取批处理大小
        /// </summary>
        public static int BatchSize => GetIntValue("BatchSize", 1000);
        
        #endregion
        
        #region 配置验证
        
        /// <summary>
        /// 验证所有必需的配置项是否存在
        /// </summary>
        /// <returns>验证结果</returns>
        public static ConfigurationValidationResult ValidateConfiguration()
        {
            var result = new ConfigurationValidationResult();
            
            // 验证数据库连接字符串
            if (!TryGetConnectionString("AlarmDatabase", out _))
            {
                result.AddError("缺少数据库连接字符串'AlarmDatabase'");
            }
            
            // 验证数值配置的合理性
            if (TopNCount <= 0)
            {
                result.AddWarning($"TopNCount配置值({TopNCount})不合理，应大于0");
            }
            
            if (FlutterTimeWindowSeconds <= 0)
            {
                result.AddWarning($"FlutterTimeWindowSeconds配置值({FlutterTimeWindowSeconds})不合理，应大于0");
            }
            
            if (FlutterThreshold <= 0)
            {
                result.AddWarning($"FlutterThreshold配置值({FlutterThreshold})不合理，应大于0");
            }
            
            if (FloodTimeWindowMinutes <= 0)
            {
                result.AddWarning($"FloodTimeWindowMinutes配置值({FloodTimeWindowMinutes})不合理，应大于0");
            }
            
            if (FloodThreshold <= 0)
            {
                result.AddWarning($"FloodThreshold配置值({FloodThreshold})不合理，应大于0");
            }
            
            if (DatabaseTimeoutSeconds <= 0)
            {
                result.AddWarning($"DatabaseTimeoutSeconds配置值({DatabaseTimeoutSeconds})不合理，应大于0");
            }
            
            if (BatchSize <= 0)
            {
                result.AddWarning($"BatchSize配置值({BatchSize})不合理，应大于0");
            }
            
            return result;
        }
        
        #endregion
    }
    
    /// <summary>
    /// 配置验证结果
    /// </summary>
    public class ConfigurationValidationResult
    {
        public List<string> Errors { get; private set; } = new List<string>();
        public List<string> Warnings { get; private set; } = new List<string>();
        
        public bool IsValid => !Errors.Any();
        public bool HasWarnings => Warnings.Any();
        
        public void AddError(string error)
        {
            if (!string.IsNullOrWhiteSpace(error))
                Errors.Add(error);
        }
        
        public void AddWarning(string warning)
        {
            if (!string.IsNullOrWhiteSpace(warning))
                Warnings.Add(warning);
        }
        
        public string GetSummary()
        {
            var summary = new System.Text.StringBuilder();
            
            if (Errors.Any())
            {
                summary.AppendLine("配置错误:");
                foreach (var error in Errors)
                {
                    summary.AppendLine($"- {error}");
                }
            }
            
            if (Warnings.Any())
            {
                summary.AppendLine("配置警告:");
                foreach (var warning in Warnings)
                {
                    summary.AppendLine($"- {warning}");
                }
            }
            
            if (!Errors.Any() && !Warnings.Any())
            {
                summary.AppendLine("配置验证通过");
            }
            
            return summary.ToString();
        }
    }
}
// {{END_MODIFICATIONS}}
